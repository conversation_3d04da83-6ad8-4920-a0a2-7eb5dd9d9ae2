/**
 * 协作监控服务
 * 用于监控协作编辑性能和状态
 */
import { EventEmitter } from '../utils/EventEmitter';
import {
  Operation,
  OperationType,
  CollaborationStatus,
  collaborationService
} from './CollaborationService';
import { conflictResolutionService } from './ConflictResolutionService';

/**
 * 协作监控配置接口
 */
export interface CollaborationMonitorConfig {
  /**
   * 是否启用性能监控
   */
  enablePerformanceMonitoring?: boolean;

  /**
   * 是否启用网络监控
   */
  enableNetworkMonitoring?: boolean;

  /**
   * 是否启用用户活动监控
   */
  enableUserActivityMonitoring?: boolean;

  /**
   * 是否启用冲突监控
   */
  enableConflictMonitoring?: boolean;

  /**
   * 是否启用操作监控
   */
  enableOperationMonitoring?: boolean;

  /**
   * 是否启用告警
   */
  enableAlerts?: boolean;

  /**
   * 采样间隔（毫秒）
   */
  sampleInterval?: number;

  /**
   * 历史数据保留时间（毫秒）
   */
  historyDuration?: number;

  /**
   * 告警阈值
   */
  alertThresholds?: {
    /**
     * 延迟阈值（毫秒）
     */
    latency?: number;

    /**
     * 丢包率阈值（0-1）
     */
    packetLoss?: number;

    /**
     * 冲突率阈值（0-1）
     */
    conflictRate?: number;

    /**
     * 操作队列长度阈值
     */
    operationQueueLength?: number;

    /**
     * 内存使用阈值（MB）
     */
    memoryUsage?: number;
  };
}

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  /**
   * 时间戳
   */
  timestamp: number;

  /**
   * 延迟（毫秒）
   */
  latency: number;

  /**
   * 丢包率（0-1）
   */
  packetLoss: number;

  /**
   * 操作队列长度
   */
  operationQueueLength: number;

  /**
   * 内存使用（MB）
   */
  memoryUsage: number;

  /**
   * 冲突数量
   */
  conflictCount: number;

  /**
   * 冲突率（0-1）
   */
  conflictRate: number;

  /**
   * 操作数量
   */
  operationCount: number;

  /**
   * 用户数量
   */
  userCount: number;

  /**
   * 连接状态
   */
  connectionStatus: CollaborationStatus;
}

/**
 * 告警级别枚举
 */
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

/**
 * 告警接口
 */
export interface Alert {
  /**
   * 告警ID
   */
  id: string;

  /**
   * 告警级别
   */
  level: AlertLevel;

  /**
   * 告警消息
   */
  message: string;

  /**
   * 告警时间
   */
  timestamp: number;

  /**
   * 告警指标
   */
  metric?: string;

  /**
   * 告警值
   */
  value?: number;

  /**
   * 告警阈值
   */
  threshold?: number;

  /**
   * 是否已确认
   */
  acknowledged?: boolean;
}

/**
 * 协作监控服务类
 */
export class CollaborationMonitorService extends EventEmitter {
  private config: Required<CollaborationMonitorConfig>;
  private enabled: boolean = false;
  private metrics: PerformanceMetrics[] = [];
  private alerts: Alert[] = [];
  private sampleTimer: number | null = null;
  private operationCount: number = 0;
  private conflictCount: number = 0;
  private lastPingTime: number = 0;
  private lastPongTime: number = 0;
  private pingInterval: number | null = null;
  private alertListeners: Map<string, (alert: Alert) => void> = new Map();

  /**
   * 创建协作监控服务
   * @param config 配置
   */
  constructor(config?: CollaborationMonitorConfig) {
    super();

    // 默认配置
    this.config = {
      enablePerformanceMonitoring: true,
      enableNetworkMonitoring: true,
      enableUserActivityMonitoring: true,
      enableConflictMonitoring: true,
      enableOperationMonitoring: true,
      enableAlerts: true,
      sampleInterval: 5000,
      historyDuration: 3600000, // 1小时
      alertThresholds: {
        latency: 500, // 500毫秒
        packetLoss: 0.1, // 10%
        conflictRate: 0.2, // 20%
        operationQueueLength: 100,
        memoryUsage: 500, // 500MB
      },
      ...config,
    };

    // 监听协作服务事件
    collaborationService.on('operation', this.handleOperation.bind(this));
    collaborationService.on('connected', this.handleConnected.bind(this));
    collaborationService.on('disconnected', this.handleDisconnected.bind(this));

    // 监听冲突解决服务事件
    conflictResolutionService.on('conflict', this.handleConflict.bind(this));
  }

  /**
   * 启用监控服务
   */
  public enable(): void {
    if (this.enabled) {
      return;
    }

    this.enabled = true;

    // 启动采样定时器
    this.startSampling();

    // 启动网络监控
    if (this.config.enableNetworkMonitoring) {
      this.startNetworkMonitoring();
    }

    console.log('协作监控服务已启用');
  }

  /**
   * 禁用监控服务
   */
  public disable(): void {
    if (!this.enabled) {
      return;
    }

    this.enabled = false;

    // 停止采样定时器
    if (this.sampleTimer !== null) {
      clearTimeout(this.sampleTimer);
      this.sampleTimer = null;
    }

    // 停止网络监控
    if (this.pingInterval !== null) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    console.log('协作监控服务已禁用');
  }

  /**
   * 获取当前性能指标
   * @returns 当前性能指标
   */
  public getCurrentMetrics(): PerformanceMetrics | null {
    if (this.metrics.length === 0) {
      return null;
    }

    return this.metrics[this.metrics.length - 1];
  }

  /**
   * 获取历史性能指标
   * @param duration 时间范围（毫秒），默认为配置的历史数据保留时间
   * @returns 历史性能指标
   */
  public getHistoryMetrics(duration: number = this.config.historyDuration): PerformanceMetrics[] {
    const now = Date.now();
    const startTime = now - duration;

    return this.metrics.filter(metric => metric.timestamp >= startTime);
  }

  /**
   * 获取活跃告警
   * @returns 活跃告警
   */
  public getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.acknowledged);
  }

  /**
   * 获取历史告警
   * @param duration 时间范围（毫秒），默认为配置的历史数据保留时间
   * @returns 历史告警
   */
  public getHistoryAlerts(duration: number = this.config.historyDuration): Alert[] {
    const now = Date.now();
    const startTime = now - duration;

    return this.alerts.filter(alert => alert.timestamp >= startTime);
  }

  /**
   * 确认告警
   * @param alertId 告警ID
   * @returns 是否成功确认
   */
  public acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (!alert) {
      return false;
    }

    alert.acknowledged = true;
    this.emit('alertAcknowledged', alert);
    return true;
  }

  /**
   * 添加告警监听器
   * @param id 监听器ID
   * @param listener 监听器函数
   */
  public addAlertListener(id: string, listener: (alert: Alert) => void): void {
    this.alertListeners.set(id, listener);
  }

  /**
   * 移除告警监听器
   * @param id 监听器ID
   */
  public removeAlertListener(id: string): void {
    this.alertListeners.delete(id);
  }

  /**
   * 清空历史数据
   */
  public clearHistory(): void {
    this.metrics = [];
    this.alerts = this.alerts.filter(alert => !alert.acknowledged);
    console.log('历史数据已清空');
  }

  /**
   * 处理操作事件
   * @param operation 操作
   */
  private handleOperation(operation: Operation): void {
    if (!this.enabled || !this.config.enableOperationMonitoring) {
      return;
    }

    this.operationCount++;
  }

  /**
   * 处理冲突事件
   * @param conflict 冲突
   */
  private handleConflict(conflict: any): void {
    if (!this.enabled || !this.config.enableConflictMonitoring) {
      return;
    }

    this.conflictCount++;

    // 计算冲突率
    const currentMetrics = this.getCurrentMetrics();
    if (currentMetrics) {
      const conflictRate = this.operationCount > 0 ? this.conflictCount / this.operationCount : 0;

      // 检查是否超过阈值
      if (this.config.enableAlerts && conflictRate > this.config.alertThresholds.conflictRate) {
        this.createAlert(
          AlertLevel.WARNING,
          `冲突率过高: ${(conflictRate * 100).toFixed(2)}%`,
          'conflictRate',
          conflictRate,
          this.config.alertThresholds.conflictRate
        );
      }
    }
  }

  /**
   * 处理连接事件
   */
  private handleConnected(): void {
    if (this.enabled) {
      // 重新启动采样
      this.startSampling();

      // 重新启动网络监控
      if (this.config.enableNetworkMonitoring) {
        this.startNetworkMonitoring();
      }

      // 创建连接恢复告警
      this.createAlert(
        AlertLevel.INFO,
        '协作连接已恢复'
      );
    }
  }

  /**
   * 处理断开连接事件
   */
  private handleDisconnected(): void {
    // 创建连接断开告警
    this.createAlert(
      AlertLevel.ERROR,
      '协作连接已断开'
    );
  }

  /**
   * 启动采样
   */
  private startSampling(): void {
    if (this.sampleTimer !== null) {
      clearTimeout(this.sampleTimer);
    }

    this.sampleTimer = window.setTimeout(() => {
      this.sampleMetrics();
      this.startSampling();
    }, this.config.sampleInterval);
  }

  /**
   * 启动网络监控
   */
  private startNetworkMonitoring(): void {
    if (this.pingInterval !== null) {
      clearInterval(this.pingInterval);
    }

    // 每10秒发送一次ping
    this.pingInterval = window.setInterval(() => {
      this.sendPing();
    }, 10000);

    // 监听pong响应
    collaborationService.on('pong', this.handlePong.bind(this));
  }

  /**
   * 发送ping
   */
  private sendPing(): void {
    if (!this.enabled || !this.config.enableNetworkMonitoring) {
      return;
    }

    this.lastPingTime = Date.now();

    // 使用协作服务发送ping操作
    collaborationService.sendOperation({
      type: OperationType.CURSOR_MOVE, // 使用光标移动作为ping
      data: {
        type: 'ping',
        timestamp: this.lastPingTime,
        x: 0,
        y: 0
      }
    });
  }

  /**
   * 处理pong响应
   * @param _data 响应数据（未使用）
   */
  private handlePong(_data: any): void {
    if (!this.enabled || !this.config.enableNetworkMonitoring) {
      return;
    }

    this.lastPongTime = Date.now();
    const latency = this.lastPongTime - this.lastPingTime;

    // 检查是否超过阈值
    if (this.config.enableAlerts && this.config.alertThresholds?.latency && latency > this.config.alertThresholds.latency) {
      this.createAlert(
        AlertLevel.WARNING,
        `网络延迟过高: ${latency}ms`,
        'latency',
        latency,
        this.config.alertThresholds.latency
      );
    }
  }

  /**
   * 采样指标
   */
  private sampleMetrics(): void {
    if (!this.enabled) {
      return;
    }

    // 计算指标
    const metrics: PerformanceMetrics = {
      timestamp: Date.now(),
      latency: this.lastPongTime > 0 ? this.lastPongTime - this.lastPingTime : 0,
      packetLoss: 0, // 需要实现计算方法
      operationQueueLength: 0, // 需要从协作服务获取
      memoryUsage: this.getMemoryUsage(),
      conflictCount: this.conflictCount,
      conflictRate: this.operationCount > 0 ? this.conflictCount / this.operationCount : 0,
      operationCount: this.operationCount,
      userCount: collaborationService.getUsers().length,
      connectionStatus: collaborationService.getStatus(),
    };

    // 添加到历史数据
    this.metrics.push(metrics);

    // 清理过期数据
    this.cleanupHistory();

    // 检查告警
    this.checkAlerts(metrics);

    // 发出指标更新事件
    this.emit('metricsUpdated', metrics);
  }

  /**
   * 清理历史数据
   */
  private cleanupHistory(): void {
    const now = Date.now();
    const cutoffTime = now - this.config.historyDuration;

    // 清理过期指标
    this.metrics = this.metrics.filter(metric => metric.timestamp >= cutoffTime);

    // 清理已确认的过期告警
    this.alerts = this.alerts.filter(alert =>
      !alert.acknowledged || alert.timestamp >= cutoffTime
    );
  }

  /**
   * 检查告警
   * @param metrics 性能指标
   */
  private checkAlerts(metrics: PerformanceMetrics): void {
    if (!this.config.enableAlerts || !this.config.alertThresholds) {
      return;
    }

    const { alertThresholds } = this.config;

    // 检查延迟
    if (alertThresholds.latency && metrics.latency > alertThresholds.latency) {
      this.createAlert(
        AlertLevel.WARNING,
        `网络延迟过高: ${metrics.latency}ms`,
        'latency',
        metrics.latency,
        alertThresholds.latency
      );
    }

    // 检查丢包率
    if (alertThresholds.packetLoss && metrics.packetLoss > alertThresholds.packetLoss) {
      this.createAlert(
        AlertLevel.WARNING,
        `网络丢包率过高: ${(metrics.packetLoss * 100).toFixed(2)}%`,
        'packetLoss',
        metrics.packetLoss,
        alertThresholds.packetLoss
      );
    }

    // 检查冲突率
    if (alertThresholds.conflictRate && metrics.conflictRate > alertThresholds.conflictRate) {
      this.createAlert(
        AlertLevel.WARNING,
        `冲突率过高: ${(metrics.conflictRate * 100).toFixed(2)}%`,
        'conflictRate',
        metrics.conflictRate,
        alertThresholds.conflictRate
      );
    }

    // 检查操作队列长度
    if (alertThresholds.operationQueueLength && metrics.operationQueueLength > alertThresholds.operationQueueLength) {
      this.createAlert(
        AlertLevel.WARNING,
        `操作队列过长: ${metrics.operationQueueLength}`,
        'operationQueueLength',
        metrics.operationQueueLength,
        alertThresholds.operationQueueLength
      );
    }

    // 检查内存使用
    if (alertThresholds.memoryUsage && metrics.memoryUsage > alertThresholds.memoryUsage) {
      this.createAlert(
        AlertLevel.WARNING,
        `内存使用过高: ${metrics.memoryUsage.toFixed(2)}MB`,
        'memoryUsage',
        metrics.memoryUsage,
        alertThresholds.memoryUsage
      );
    }

    // 检查连接状态
    if (metrics.connectionStatus === CollaborationStatus.ERROR) {
      this.createAlert(
        AlertLevel.ERROR,
        '协作连接出错'
      );
    } else if (metrics.connectionStatus === CollaborationStatus.DISCONNECTED) {
      this.createAlert(
        AlertLevel.ERROR,
        '协作连接已断开'
      );
    }
  }

  /**
   * 创建告警
   * @param level 告警级别
   * @param message 告警消息
   * @param metric 告警指标
   * @param value 告警值
   * @param threshold 告警阈值
   */
  private createAlert(
    level: AlertLevel,
    message: string,
    metric?: string,
    value?: number,
    threshold?: number
  ): void {
    // 创建告警对象
    const alert: Alert = {
      id: this.generateId(),
      level,
      message,
      timestamp: Date.now(),
      metric,
      value,
      threshold,
      acknowledged: false,
    };

    // 添加到告警列表
    this.alerts.push(alert);

    // 发出告警事件
    this.emit('alert', alert);

    // 通知告警监听器
    for (const listener of this.alertListeners.values()) {
      listener(alert);
    }

    console.log(`协作告警: [${level}] ${message}`);
  }

  /**
   * 获取内存使用
   * @returns 内存使用（MB）
   */
  private getMemoryUsage(): number {
    // 在浏览器环境中
    if (typeof window !== 'undefined' && window.performance && (window.performance as any).memory) {
      const memory = (window.performance as any).memory;
      return memory.usedJSHeapSize / (1024 * 1024);
    }

    return 0;
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
}
