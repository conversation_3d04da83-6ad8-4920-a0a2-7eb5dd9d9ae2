/**
 * 冲突预防服务
 * 提供实时显示其他用户正在编辑的区域，以减少冲突的发生
 */
import { EventEmitter } from '../utils/EventEmitter';
import { store } from '../store';
import {
  addEditingZone,
  removeEditingZone,
  clearEditingZones,
  updateEditingZone
} from '../store/collaboration/editingZonesSlice';
import { collaborationService, Operation, OperationType } from './CollaborationService';

/**
 * 编辑区域类型枚举
 */
export enum EditingZoneType {
  ENTITY = 'entity',
  COMPONENT = 'component',
  PROPERTY = 'property',
  SCENE = 'scene',
  RESOURCE = 'resource'
}

/**
 * 编辑区域接口
 */
export interface EditingZone {
  id: string;
  userId: string;
  userName: string;
  type: EditingZoneType;
  entityId?: string;
  componentId?: string;
  propertyPath?: string[];
  resourceId?: string;
  startTime: number;
  lastUpdateTime: number;
  color: string;
}

/**
 * 冲突预防服务类
 */
class ConflictPreventionService extends EventEmitter {
  private enabled: boolean = false;
  private editingZones: Map<string, EditingZone> = new Map();
  private zoneExpirationTime: number = 10000; // 10秒后过期
  private cleanupInterval: number | null = null;
  private currentUserId: string = '';
  
  // 用户颜色映射
  private userColors: Map<string, string> = new Map();
  private colorPalette: string[] = [
    '#f44336', '#e91e63', '#9c27b0', '#673ab7', 
    '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4',
    '#009688', '#4caf50', '#8bc34a', '#cddc39',
    '#ffeb3b', '#ffc107', '#ff9800', '#ff5722'
  ];

  constructor() {
    super();
  }

  /**
   * 初始化服务
   * @param userId 当前用户ID
   */
  public initialize(userId: string): void {
    this.currentUserId = userId;
    
    // 监听协作服务事件
    collaborationService.on('operation', this.handleOperation.bind(this));
    collaborationService.on('userLeft', this.handleUserLeft.bind(this));
    
    // 启动清理定时器
    this.startCleanupTimer();
  }

  /**
   * 设置是否启用冲突预防
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    
    if (!enabled) {
      this.clearAllEditingZones();
      this.stopCleanupTimer();
    } else {
      this.startCleanupTimer();
    }
  }

  /**
   * 设置区域过期时间
   * @param timeMs 过期时间（毫秒）
   */
  public setZoneExpirationTime(timeMs: number): void {
    this.zoneExpirationTime = timeMs;
  }

  /**
   * 广播本地编辑区域
   * @param type 区域类型
   * @param data 区域数据
   */
  public broadcastEditingZone(
    type: EditingZoneType,
    data: {
      entityId?: string;
      componentId?: string;
      propertyPath?: string[];
      resourceId?: string;
    }
  ): void {
    if (!this.enabled) return;
    
    // 发送编辑区域操作
    collaborationService.sendOperation({
      type: OperationType.EDITING_ZONE_UPDATE,
      data: {
        zoneType: type,
        ...data
      }
    });
  }

  /**
   * 清除本地编辑区域
   * @param type 区域类型
   * @param data 区域数据
   */
  public clearEditingZone(
    type: EditingZoneType,
    data: {
      entityId?: string;
      componentId?: string;
      propertyPath?: string[];
      resourceId?: string;
    }
  ): void {
    if (!this.enabled) return;
    
    // 发送清除编辑区域操作
    collaborationService.sendOperation({
      type: OperationType.EDITING_ZONE_CLEAR,
      data: {
        zoneType: type,
        ...data
      }
    });
  }

  /**
   * 获取所有编辑区域
   * @returns 编辑区域数组
   */
  public getAllEditingZones(): EditingZone[] {
    return Array.from(this.editingZones.values());
  }

  /**
   * 获取实体的编辑区域
   * @param entityId 实体ID
   * @returns 编辑区域数组
   */
  public getEntityEditingZones(entityId: string): EditingZone[] {
    return Array.from(this.editingZones.values())
      .filter(zone => zone.entityId === entityId);
  }

  /**
   * 清除所有编辑区域
   */
  public clearAllEditingZones(): void {
    this.editingZones.clear();
    store.dispatch(clearEditingZones());
  }

  /**
   * 处理操作
   * @private
   */
  private handleOperation(operation: Operation): void {
    if (!this.enabled) return;
    
    // 忽略自己的操作
    if (operation.userId === this.currentUserId) return;
    
    const { type, data } = operation;
    
    switch (type) {
      case OperationType.EDITING_ZONE_UPDATE:
        this.handleEditingZoneUpdate(operation);
        break;
        
      case OperationType.EDITING_ZONE_CLEAR:
        this.handleEditingZoneClear(operation);
        break;
        
      case OperationType.ENTITY_UPDATE:
      case OperationType.COMPONENT_UPDATE:
      case OperationType.PROPERTY_UPDATE:
        // 自动创建编辑区域
        this.createEditingZoneFromOperation(operation);
        break;
    }
  }

  /**
   * 处理编辑区域更新
   * @private
   */
  private handleEditingZoneUpdate(operation: Operation): void {
    const { userId, userName, data } = operation;
    
    if (!data || !data.zoneType) return;
    
    const zoneType = data.zoneType as EditingZoneType;
    const zoneId = this.generateZoneId(userId, zoneType, data);
    
    // 检查是否已存在
    const existingZone = this.editingZones.get(zoneId);
    
    if (existingZone) {
      // 更新现有区域
      existingZone.lastUpdateTime = Date.now();
      this.editingZones.set(zoneId, existingZone);
      store.dispatch(updateEditingZone({
        id: zoneId,
        lastUpdateTime: existingZone.lastUpdateTime
      }));
    } else {
      // 创建新区域
      const color = this.getUserColor(userId);
      
      const newZone: EditingZone = {
        id: zoneId,
        userId,
        userName: userName || userId,
        type: zoneType,
        entityId: data.entityId,
        componentId: data.componentId,
        propertyPath: data.propertyPath,
        resourceId: data.resourceId,
        startTime: Date.now(),
        lastUpdateTime: Date.now(),
        color
      };
      
      this.editingZones.set(zoneId, newZone);
      store.dispatch(addEditingZone(newZone));
      
      this.emit('editingZoneCreated', newZone);
    }
  }

  /**
   * 处理编辑区域清除
   * @private
   */
  private handleEditingZoneClear(operation: Operation): void {
    const { userId, data } = operation;
    
    if (!data || !data.zoneType) return;
    
    const zoneType = data.zoneType as EditingZoneType;
    const zoneId = this.generateZoneId(userId, zoneType, data);
    
    // 移除区域
    this.editingZones.delete(zoneId);
    store.dispatch(removeEditingZone(zoneId));
    
    this.emit('editingZoneRemoved', zoneId);
  }

  /**
   * 从操作创建编辑区域
   * @private
   */
  private createEditingZoneFromOperation(operation: Operation): void {
    const { type, userId, userName, data } = operation;
    
    if (!data) return;
    
    let zoneType: EditingZoneType;
    const zoneData: any = {};
    
    switch (type) {
      case OperationType.ENTITY_UPDATE:
        zoneType = EditingZoneType.ENTITY;
        zoneData.entityId = data.entityId;
        break;
        
      case OperationType.COMPONENT_UPDATE:
        zoneType = EditingZoneType.COMPONENT;
        zoneData.entityId = data.entityId;
        zoneData.componentId = data.componentId;
        break;
        
      case OperationType.PROPERTY_UPDATE:
        zoneType = EditingZoneType.PROPERTY;
        zoneData.entityId = data.entityId;
        zoneData.componentId = data.componentId;
        zoneData.propertyPath = data.path;
        break;
        
      default:
        return;
    }
    
    const zoneId = this.generateZoneId(userId, zoneType, zoneData);
    
    // 检查是否已存在
    const existingZone = this.editingZones.get(zoneId);
    
    if (existingZone) {
      // 更新现有区域
      existingZone.lastUpdateTime = Date.now();
      this.editingZones.set(zoneId, existingZone);
      store.dispatch(updateEditingZone({
        id: zoneId,
        lastUpdateTime: existingZone.lastUpdateTime
      }));
    } else {
      // 创建新区域
      const color = this.getUserColor(userId);
      
      const newZone: EditingZone = {
        id: zoneId,
        userId,
        userName: userName || userId,
        type: zoneType,
        ...zoneData,
        startTime: Date.now(),
        lastUpdateTime: Date.now(),
        color
      };
      
      this.editingZones.set(zoneId, newZone);
      store.dispatch(addEditingZone(newZone));
      
      this.emit('editingZoneCreated', newZone);
    }
  }

  /**
   * 处理用户离开
   * @private
   */
  private handleUserLeft(userId: string): void {
    // 移除该用户的所有编辑区域
    const zonesToRemove: string[] = [];
    
    this.editingZones.forEach((zone, id) => {
      if (zone.userId === userId) {
        zonesToRemove.push(id);
      }
    });
    
    zonesToRemove.forEach(id => {
      this.editingZones.delete(id);
      store.dispatch(removeEditingZone(id));
      this.emit('editingZoneRemoved', id);
    });
  }

  /**
   * 启动清理定时器
   * @private
   */
  private startCleanupTimer(): void {
    if (this.cleanupInterval !== null) {
      this.stopCleanupTimer();
    }
    
    this.cleanupInterval = window.setInterval(() => {
      this.cleanupExpiredZones();
    }, 5000); // 每5秒检查一次
  }

  /**
   * 停止清理定时器
   * @private
   */
  private stopCleanupTimer(): void {
    if (this.cleanupInterval !== null) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * 清理过期的编辑区域
   * @private
   */
  private cleanupExpiredZones(): void {
    const now = Date.now();
    const expiredZones: string[] = [];
    
    this.editingZones.forEach((zone, id) => {
      if (now - zone.lastUpdateTime > this.zoneExpirationTime) {
        expiredZones.push(id);
      }
    });
    
    expiredZones.forEach(id => {
      this.editingZones.delete(id);
      store.dispatch(removeEditingZone(id));
      this.emit('editingZoneExpired', id);
    });
  }

  /**
   * 生成区域ID
   * @private
   */
  private generateZoneId(userId: string, type: EditingZoneType, data: any): string {
    let id = `${userId}:${type}`;
    
    if (data.entityId) id += `:${data.entityId}`;
    if (data.componentId) id += `:${data.componentId}`;
    if (data.propertyPath) id += `:${data.propertyPath.join('.')}`;
    if (data.resourceId) id += `:${data.resourceId}`;
    
    return id;
  }

  /**
   * 获取用户颜色
   * @private
   */
  private getUserColor(userId: string): string {
    if (this.userColors.has(userId)) {
      return this.userColors.get(userId)!;
    }
    
    // 分配新颜色
    const colorIndex = this.userColors.size % this.colorPalette.length;
    const color = this.colorPalette[colorIndex];
    
    this.userColors.set(userId, color);
    return color;
  }
}

// 导出单例
export const conflictPreventionService = new ConflictPreventionService();
